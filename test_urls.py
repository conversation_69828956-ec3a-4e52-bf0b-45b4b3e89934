#!/usr/bin/env python3
"""Test URL detection for Facebook and TikTok."""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.domain.value_objects.url import Url

def test_facebook_urls():
    """Test Facebook URL detection."""
    print("=== Facebook URL Tests ===")
    
    facebook_urls = [
        'https://www.facebook.com/share/r/16aMkuFYki/',
        'https://fb.watch/shortcode123/',
        'https://www.facebook.com/username/videos/123456789/',
        'https://facebook.com/share/r/abc123/'
    ]
    
    for url_str in facebook_urls:
        try:
            url = Url(url_str)
            print(f'URL: {url_str}')
            print(f'  is_facebook: {url.is_facebook}')
            print(f'  shortcode: {url.extract_facebook_shortcode()}')
            print()
        except Exception as e:
            print(f'ERROR with {url_str}: {e}')
            print()

def test_tiktok_urls():
    """Test TikTok URL detection."""
    print("=== TikTok URL Tests ===")
    
    tiktok_urls = [
        'https://vt.tiktok.com/ZSHgdHCKUNXQd-SWMeo/',
        'https://www.tiktok.com/@username/video/1234567890123456789',
        'https://vm.tiktok.com/SHORTCODE/',
        'https://tiktok.com/@user/video/123456789'
    ]
    
    for url_str in tiktok_urls:
        try:
            url = Url(url_str)
            print(f'URL: {url_str}')
            print(f'  is_tiktok: {url.is_tiktok}')
            print(f'  video_id: {url.extract_tiktok_video_id()}')
            print()
        except Exception as e:
            print(f'ERROR with {url_str}: {e}')
            print()

if __name__ == "__main__":
    test_facebook_urls()
    test_tiktok_urls()
