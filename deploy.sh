#!/bin/bash

# Deployment script for Saver Bot
# This script commits changes, pushes to git, and deploys to server

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SERVER_IP="************"  # Change to your VPS IP if different
SERVER_USER="root"
APP_PATH="/root/apps/instagram_service"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if there are changes to commit
check_changes() {
    if [ -n "$(git status --porcelain)" ]; then
        return 0  # Has changes
    else
        return 1  # No changes
    fi
}

# Function to get current branch
get_current_branch() {
    git branch --show-current
}

# Function to commit and push changes
commit_and_push() {
    local current_branch=$(get_current_branch)
    
    print_status "Checking for changes to commit..."
    
    if check_changes; then
        print_status "Changes detected. Committing and pushing..."
        
        # Add all changes
        git add .
        
        # Get list of changed files for commit message
        local changed_files=$(git diff --cached --name-only | head -5 | tr '\n' ' ')
        if [ ${#changed_files} -gt 50 ]; then
            changed_files="${changed_files:0:47}..."
        fi
        
        # Create commit message with timestamp
        local commit_message="Auto-deploy: $(date '+%Y-%m-%d %H:%M:%S') - $changed_files"
        
        # Commit changes
        git commit -m "$commit_message"
        
        # Push to remote
        print_status "Pushing to remote repository..."
        git push origin "$current_branch"
        
        print_success "Changes committed and pushed successfully"
    else
        print_warning "No changes to commit"
    fi
}

# Function to deploy to server
deploy_to_server() {
    print_status "Deploying to server $SERVER_IP..."
    
    # Deploy using SSH
    ssh -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_IP" << 'EOF'
        set -e
        
        # Colors for server output
        GREEN='\033[0;32m'
        YELLOW='\033[1;33m'
        BLUE='\033[0;34m'
        NC='\033[0m'
        
        echo -e "${BLUE}[SERVER]${NC} Starting deployment..."
        
        # Navigate to app directory
        cd /root/apps/instagram_service
        
        # Check if directory exists
        if [ ! -d "/root/apps/instagram_service" ]; then
            echo -e "${YELLOW}[SERVER]${NC} App directory not found. Creating..."
            mkdir -p /root/apps/instagram_service
            cd /root/apps/instagram_service
            <NAME_EMAIL>:Muhammadali-Akbarov/instagram_service.git
        fi
        
        # Pull latest changes
        echo -e "${BLUE}[SERVER]${NC} Pulling latest changes..."
        git pull origin main  # or your default branch
        
        # Install/update dependencies if requirements.txt exists
        if [ -f "requirements.txt" ]; then
            echo -e "${BLUE}[SERVER]${NC} Installing/updating dependencies..."
            
            # Activate virtual environment if it exists
            if [ -d "venv" ]; then
                echo -e "${BLUE}[SERVER]${NC} Activating virtual environment..."
                source venv/bin/activate
            fi
            
            pip3 install -r requirements.txt
        fi
        
        # Restart service if it's running as a systemd service
        if systemctl is-active --quiet instagram_service; then
            echo -e "${BLUE}[SERVER]${NC} Restarting service..."
            systemctl restart instagram_service
            systemctl restart instagram-celery
        else
            echo -e "${YELLOW}[SERVER]${NC} Service not running as systemd service"
        fi
        
        echo -e "${GREEN}[SERVER]${NC} Deployment completed successfully!"
EOF
    
    if [ $? -eq 0 ]; then
        print_success "Deployment completed successfully!"
    else
        print_error "Deployment failed!"
        exit 1
    fi
}

# Main execution
main() {
    print_status "Starting deployment process..."
    
    # Check if we're in a git repository
    if [ ! -d ".git" ]; then
        print_error "Not in a git repository. Please run this script from the project root."
        exit 1
    fi
    
    # Check if git is configured
    if ! git config user.name > /dev/null 2>&1; then
        print_error "Git user.name is not configured. Please configure git first."
        exit 1
    fi
    
    # Step 1: Commit and push changes
    commit_and_push
    
    # Step 2: Deploy to server
    deploy_to_server
    
    print_success "Deployment process completed successfully!"
}

# Run main function
main "$@"