2025-07-06 12:56:03,339 - __main__ - INFO - Starting InstaSaver API server...
2025-07-06 12:56:03,339 - __main__ - INFO - Server will run on 0.0.0.0:3333
2025-07-06 12:56:13,272 - __main__ - INFO - Starting InstaSaver API server...
2025-07-06 12:56:13,272 - __main__ - INFO - Server will run on 0.0.0.0:3333
2025-07-06 12:56:15,527 - src.presentation.controllers.media_controller - INFO - Received async download request for URL: https://pin.it/15LwevBbF Data: {'chat_id': 2105729169, 'url': 'https://pin.it/15LwevBbF', 'bot_token': '7940308044:AAGn8QHcLdNcFdZCINV7LVONzP8xy619cJY', 'media_type': <MediaType.AUTO: 'auto'>, 'video_format': None}
2025-07-06 12:59:00,074 - src.presentation.controllers.media_controller - INFO - Received async download request for URL: https://pin.it/15LwevBbF Data: {'chat_id': 2105729169, 'url': 'https://pin.it/15LwevBbF', 'bot_token': '7940308044:AAGn8QHcLdNcFdZCINV7LVONzP8xy619cJY', 'media_type': <MediaType.AUTO: 'auto'>, 'video_format': None}
2025-07-06 12:59:07,079 - src.presentation.controllers.media_controller - INFO - Getting status for task: b6d0a34c-3987-42d7-905a-c6c3c29f5db6
2025-07-06 12:59:28,725 - src.presentation.controllers.media_controller - INFO - Getting status for task: b6d0a34c-3987-42d7-905a-c6c3c29f5db6
