"""Service interfaces."""
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any

from src.application.dtos.download_dtos import DownloadMediaRequest
from ..entities.media_item import MediaItem, Album
from ..entities.download_result import DownloadResult
from ..entities.music_search_result import MusicSearchResponse
from ..value_objects.url import Url
from ..value_objects.identifiers import ChatId


class IMediaDownloadService(ABC):
    """Interface for media download services."""

    @abstractmethod
    async def get_media_info(self, url: Url) -> MediaItem:
        """Get media information from URL."""
        pass

    @abstractmethod
    async def download_media(self, media_item: MediaItem) -> bytes:
        """Download media content."""
        pass


class IInstagramService(IMediaDownloadService):
    """Interface for Instagram service."""

    @abstractmethod
    async def get_album_info(self, url: Url) -> Album:
        """Get album information from Instagram URL."""
        pass


class IYouTubeService(IMediaDownloadService):
    """Interface for YouTube service."""

    @abstractmethod
    async def download_video(self, url: Url, bot_username: str = "instasaver_bot", video_format: Optional[str] = None) -> DownloadResult:
        """Download YouTube video."""
        pass

    @abstractmethod
    async def download_audio(self, url: Url, bot_username: str = "instasaver_bot", video_format: Optional[str] = None) -> DownloadResult:
        """Download YouTube audio."""
        pass


class ITelegramService(ABC):
    """Interface for Telegram service."""

    @abstractmethod
    async def send_photo(
        self, 
        chat_id: ChatId, 
        photo_path: str, 
        caption: Optional[str] = None,
        message_thread_id: Optional[int] = None,
    ) -> str:
        """Send photo to Telegram chat. Returns file_id."""
        pass

    @abstractmethod
    async def send_video(
        self, 
        chat_id: ChatId, 
        video_path: str, 
        caption: Optional[str] = None,
        duration: Optional[int] = None,
        width: Optional[int] = None,
        height: Optional[int] = None,
        thumbnail_path: Optional[str] = None,
        message_thread_id: Optional[int] = None,
    ) -> str:
        """Send video to Telegram chat. Returns file_id."""
        pass

    @abstractmethod
    async def send_audio(
        self, 
        chat_id: ChatId, 
        audio_path: str, 
        caption: Optional[str] = None,
        duration: Optional[int] = None,
        title: Optional[str] = None,
        performer: Optional[str] = None,
        message_thread_id: Optional[int] = None,
    ) -> str:
        """Send audio to Telegram chat. Returns file_id."""
        pass

    @abstractmethod
    async def send_message(self, chat_id: ChatId, text: str, message_thread_id: Optional[int] = None) -> None:
        """Send text message to Telegram chat."""
        pass

    @abstractmethod
    async def send_audio_by_file_id(
        self,
        chat_id: ChatId,
        file_id: str,
        caption: Optional[str] = None,
        title: Optional[str] = None,
        performer: Optional[str] = None,
        duration: Optional[int] = None,
        message_thread_id: Optional[int] = None
    ) -> str:
        """Send audio to Telegram chat using file_id. Returns file_id."""
        pass

    @abstractmethod
    async def send_video_by_file_id(
        self,
        chat_id: ChatId,
        file_id: str,
        caption: Optional[str] = None,
        message_thread_id: Optional[int] = None
    ) -> str:
        """Send video to Telegram chat using file_id. Returns file_id."""
        pass

    @abstractmethod
    async def get_bot_username_public(self) -> str:
        """Get bot username."""
        pass


class IFileService(ABC):
    """Interface for file operations."""

    @abstractmethod
    async def generate_unique_filename(self, extension: str) -> str:
        """Generate unique filename."""
        pass

    @abstractmethod
    async def cleanup_file(self, file_path: str) -> None:
        """Clean up temporary file."""
        pass

    @abstractmethod
    async def ensure_directory_exists(self, directory: str) -> None:
        """Ensure directory exists."""
        pass

    @abstractmethod
    async def save_file(self, content: bytes, filename: str) -> str:
        """Save file content to storage. Returns full file path."""
        pass


class IMusicSearchService(ABC):
    """Interface for music search service."""

    @abstractmethod
    async def search_music(self, query: str, page: int = 1) -> MusicSearchResponse:
        """Search for music by query."""
        pass

    @abstractmethod
    async def download_from_shortcode(self, shortcode: str, media_type: str = "audio") -> DownloadResult:
        """Download media from YouTube shortcode."""
        pass


class IPinterestService(IMediaDownloadService):
    """Interface for Pinterest service."""
    pass


class ITikTokService(IMediaDownloadService):
    """Interface for TikTok service."""
    pass


class ILikeeService(IMediaDownloadService):
    """Interface for Likee service."""
    pass


class IFacebookService(IMediaDownloadService):
    """Interface for Facebook service."""
    pass


class IAsyncDownloadService(ABC):
    """Interface for async download service."""

    @abstractmethod
    def queue_download_task(self, request: DownloadMediaRequest) -> Dict[str, Any]:
        """Queue a download task for background processing."""
        pass
