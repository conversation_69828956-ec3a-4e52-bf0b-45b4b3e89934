"""Media download controller."""
import logging
from datetime import datetime
from fastapi import APIRouter, Request
from fastapi.responses import JSONResponse

from src.presentation.models.request_models import DownloadRequest, DownloadFromShortcodeRequest
from src.presentation.models.response_models import DownloadResponse, ErrorResponse, HealthResponse, SearchMusicResponse, MusicSearchResult, AsyncTaskResponse, TaskStatusResponse
from src.application.dtos.download_dtos import DownloadMediaRequest
from src.application.dtos.search_dtos import SearchMusicRequest as SearchMusicRequestDto, DownloadFromShortcodeRequest as DownloadFromShortcodeRequestDto
from src.presentation.dependencies import (
    get_download_media_use_case,
    get_search_music_use_case,
    get_download_from_shortcode_use_case,
    get_error_notification_service,
    get_async_download_service
)


async def extract_bot_token(request: Request) -> str:
    """Extract bot token from request."""
    # Try to get bot token from headers
    bot_token = request.headers.get("X-Bot-Token") or request.headers.get("Authorization")
    if bot_token and bot_token.startswith("Bearer "):
        bot_token = bot_token[7:]  # Remove "Bearer " prefix

    # If not in headers, try to get from query params
    if not bot_token:
        bot_token = request.query_params.get("bot_token")

    return bot_token

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api", tags=["media"])


@router.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""
    return HealthResponse(
        status="healthy",
        version="1.0.0",
        timestamp=datetime.now().isoformat()
    )

@router.post("/download-async", response_model=AsyncTaskResponse)
async def download_media_async(request: DownloadRequest):
    """Queue a download task for background processing."""
    try:
        logger.info(f"Received async download request for URL: {request.url} Data: {request.model_dump()}")
        print(f"Received async download request for URL: {request.url} Data: {request.model_dump()}")

        async_service = get_async_download_service()

        app_request = DownloadMediaRequest(
            chat_id=request.chat_id,
            url=request.url,
            bot_token=request.bot_token,
            media_type=request.media_type.value,
            video_format=request.video_format
        )

        result = async_service.queue_download_task(app_request)

        if result['success']:
            return AsyncTaskResponse(
                status="queued",
                message=result['message'],
                task_id=result['task_id']
            )

        return JSONResponse(
            status_code=400,
            content=ErrorResponse(
                status="error",
                message=result['message']
            ).model_dump()
        )

    except Exception as exc:
        logger.error(f"Error in async download: {exc}")
        return JSONResponse(
            status_code=500,
            content=ErrorResponse(
                status="error",
                message="Failed to queue download task"
            ).model_dump()
        )


@router.post("/download", response_model=DownloadResponse)
async def download_media(request: DownloadRequest):
    """Download media from Instagram or YouTube and send to Telegram."""
    try:
        logger.info(f"Received download request for URL: {request.url}")

        use_case = get_download_media_use_case(request.bot_token)

        app_request = DownloadMediaRequest(
            chat_id=request.chat_id,
            url=request.url,
            bot_token=request.bot_token,
            media_type=request.media_type.value,
            video_format=request.video_format
        )

        result = await use_case.execute(app_request)

        if result.success:
            response = DownloadResponse(
                status="success",
                message=result.message,
                file_id=result.file_id
            )

            return response

        try:
            error_service = get_error_notification_service(request.bot_token)
            await error_service.notify_admin_error(f"[POST /download] Download failed: {result.message}\nRequest: {request.model_dump()}")
        except Exception as notification_error:
            logger.error(f"Failed to send error notification: {notification_error}")

        response = JSONResponse(
            status_code=400,
            content=ErrorResponse(
                status="error",
                message=result.message
            ).model_dump()
        )

        return response

    except ValueError as exc:
        logger.error(f"Validation error: {exc}")

        try:
            error_service = get_error_notification_service(request.bot_token)
            await error_service.notify_admin_error(f"[POST /download] Validation error in download: {str(exc)}\nRequest: {request.model_dump()}")
        except Exception as notification_error:
            logger.error(f"Failed to send error notification: {notification_error}")

        return JSONResponse(
            status_code=400,
            content=ErrorResponse(
                status="error",
                message="Noto'g'ri ma'lumot kiritildi. Iltimos, to'g'ri formatda kiriting."
            ).model_dump()
        )

    except Exception as exc:
        logger.error(f"Unexpected error in media controller: {exc}")

        try:
            error_service = get_error_notification_service(request.bot_token)
            await error_service.notify_admin_error(f"[POST /download] Download endpoint error: {str(exc)}\nRequest: {request.model_dump()}")
        except Exception as notification_error:
            logger.error(f"Failed to send error notification: {notification_error}")

        user_message = "Serverda xatolik yuz berdi. Iltimos, keyinroq qayta urinib ko'ring."

        return JSONResponse(
            status_code=500,
            content=ErrorResponse(
                status="error",
                message=user_message
            ).model_dump()
        )


@router.get("/search-music", response_model=SearchMusicResponse)
async def search_music(query: str, page: int = 1):
    """Search for music using the FastSaver API."""    
    try:
        logger.info(f"Received music search request for query: {query}, page: {page}")

        use_case = get_search_music_use_case()

        app_request = SearchMusicRequestDto(
            query=query,
            page=page
        )

        result = await use_case.execute(app_request)

        search_results = [
            MusicSearchResult(
                title=item.title,
                shortcode=item.shortcode,
                duration=item.duration,
                thumb=item.thumb,
                thumb_best=item.thumb_best
            )
            for item in result.results
        ]

        response = SearchMusicResponse(
            error=result.error,
            page=result.page,
            results=search_results
        )

        return response

    except ValueError as exc:
        logger.error(f"Validation error: {exc}")

        return JSONResponse(
            status_code=400,
            content=ErrorResponse(
                status="error",
                message="Noto'g'ri ma'lumot kiritildi. Iltimos, to'g'ri formatda kiriting."
            ).model_dump()
        )

    except Exception as exc:
        logger.error(f"Unexpected error in search music: {exc}")

        return JSONResponse(
            status_code=500,
            content=ErrorResponse(
                status="error",
                message="Musiqa qidiruvida xatolik yuz berdi. Iltimos, keyinroq qayta urinib ko'ring."
            ).model_dump()
        )


@router.post("/download-shortcode", response_model=DownloadResponse)
async def download_from_shortcode(request: DownloadFromShortcodeRequest):
    """Download media from YouTube shortcode and send to Telegram."""
    try:
        logger.info(f"Received download from shortcode request: {request.shortcode}")

        use_case = get_download_from_shortcode_use_case(request.bot_token)

        app_request = DownloadFromShortcodeRequestDto(
            chat_id=request.chat_id,
            shortcode=request.shortcode,
            bot_token=request.bot_token,
            media_type=request.media_type.value
        )

        result = await use_case.execute(app_request)

        if result.success:
            response = DownloadResponse(
                status="success",
                message=result.message,
                file_id=result.file_id
            )
            return response

        try:
            error_service = get_error_notification_service(request.bot_token)
            await error_service.notify_admin_error(f"[POST /download-shortcode] Download failed: {result.message}\nRequest: {request.model_dump()}")
        except Exception as notification_error:
            logger.error(f"Failed to send error notification: {notification_error}")
        response = JSONResponse(
            status_code=400,
            content=ErrorResponse(
                status="error",
                message=result.message
            ).model_dump()
        )
        return response

    except ValueError as e:
        logger.error(f"Validation error: {e}")

        try:
            error_service = get_error_notification_service(request.bot_token)
            await error_service.notify_admin_error(f"[POST /download-shortcode] Validation error in download shortcode: {str(e)}\nRequest: {request.model_dump()}")
        except Exception as notification_error:
            logger.error(f"Failed to send error notification: {notification_error}")

        return JSONResponse(
            status_code=400,
            content=ErrorResponse(
                status="error",
                message="Noto'g'ri ma'lumot kiritildi. Iltimos, to'g'ri formatda kiriting."
            ).model_dump()
        )
    except Exception as e:
        logger.error(f"Unexpected error in download from shortcode: {e}")

        try:
            error_service = get_error_notification_service(request.bot_token)
            await error_service.notify_admin_error(f"[POST /download-shortcode] Validation error in download shortcode: {str(e)}\nRequest: {request.model_dump()}")
        except Exception as notification_error:
            logger.error(f"Failed to send error notification: {notification_error}")

        return JSONResponse(
            status_code=500,
            content=ErrorResponse(
                status="error",
                message="Serverda xatolik yuz berdi. Iltimos, keyinroq qayta urinib ko'ring."
            ).model_dump()
        )


# Async download endpoints
@router.post("/download-async", response_model=AsyncTaskResponse)
async def download_media_async(request: DownloadRequest):
    """Queue a download task for background processing."""
    try:
        logger.info(f"Received async download request for URL: {request.url}")

        async_service = get_async_download_service()

        app_request = DownloadMediaRequest(
            chat_id=request.chat_id,
            url=request.url,
            bot_token=request.bot_token,
            media_type=request.media_type.value,
            video_format=request.video_format
        )

        result = async_service.queue_download_task(app_request)

        if result['success']:
            return AsyncTaskResponse(
                status="queued",
                message=result['message'],
                task_id=result['task_id']
            )

        return JSONResponse(
            status_code=400,
            content=ErrorResponse(
                status="error",
                message=result['message']
            ).model_dump()
        )

    except Exception as exc:
        logger.error(f"Error in async download: {exc}")
        return JSONResponse(
            status_code=500,
            content=ErrorResponse(
                status="error",
                message="Failed to queue download task"
            ).model_dump()
        )


@router.post("/download-shortcode-async", response_model=AsyncTaskResponse)
async def download_from_shortcode_async(request: DownloadFromShortcodeRequest):
    """Queue a shortcode download task for background processing."""
    try:
        logger.info(f"Received async shortcode download request: {request.shortcode}")

        async_service = get_async_download_service()

        app_request = DownloadFromShortcodeRequestDto(
            chat_id=request.chat_id,
            shortcode=request.shortcode,
            bot_token=request.bot_token,
            media_type=request.media_type.value
        )

        result = async_service.queue_shortcode_download_task(app_request)

        if result['success']:
            return AsyncTaskResponse(
                status="queued",
                message=result['message'],
                task_id=result['task_id']
            )

        return JSONResponse(
            status_code=400,
            content=ErrorResponse(
                status="error",
                message=result['message']
            ).model_dump()
        )

    except Exception as exc:
        logger.error(f"Error in async shortcode download: {exc}")
        return JSONResponse(
            status_code=500,
            content=ErrorResponse(
                status="error",
                message="Failed to queue shortcode download task"
            ).model_dump()
        )


@router.get("/task-status/{task_id}", response_model=TaskStatusResponse)
async def get_task_status(task_id: str):
    """Get the status of a Celery task."""
    try:
        logger.info(f"Getting status for task: {task_id}")

        async_service = get_async_download_service()
        status = async_service.get_task_status(task_id)

        return TaskStatusResponse(**status)

    except Exception as exc:
        logger.error(f"Error getting task status: {exc}")
        return JSONResponse(
            status_code=500,
            content=ErrorResponse(
                status="error",
                message="Failed to get task status"
            ).model_dump()
        )


@router.delete("/task-cancel/{task_id}")
async def cancel_task(task_id: str):
    """Cancel a Celery task."""
    try:
        logger.info(f"Cancelling task: {task_id}")

        async_service = get_async_download_service()
        result = async_service.cancel_task(task_id)

        if result['success']:
            return {"message": result['message']}

        return JSONResponse(
            status_code=400,
            content=ErrorResponse(
                status="error",
                message=result['message']
            ).model_dump()
        )

    except Exception as exc:
        logger.error(f"Error cancelling task: {exc}")
        return JSONResponse(
            status_code=500,
            content=ErrorResponse(
                status="error",
                message="Failed to cancel task"
            ).model_dump()
        )
