"""Dependency injection container."""
from typing import Dict, Any

from src.application.use_cases.download_media_use_case import DownloadMediaUseCase
from src.application.use_cases.search_music_use_case import SearchMusicUseCase
from src.application.use_cases.download_from_shortcode_use_case import DownloadFromShortcodeUseCase
from src.infrastructure.services.instagram_service import InstagramService
from src.infrastructure.services.youtube_service import YouTubeService
from src.infrastructure.services.telegram_service import TelegramService
from src.infrastructure.services.file_service import FileService
from src.infrastructure.services.music_search_service import MusicSearchService
from src.infrastructure.services.error_notification_service import ErrorNotificationService
from src.infrastructure.services.async_download_service import AsyncDownloadService

from src.domain.value_objects.identifiers import BotToken
from src.infrastructure.services.pinterest_service import PinterestService
from src.infrastructure.services.tiktok_service import TikTokService
from src.infrastructure.services.likee_service import LikeeService


class Container:
    """Simple dependency injection container."""

    def __init__(self):
        self._services: Dict[str, Any] = {}
        self._singletons: Dict[str, Any] = {}
        self._setup_services()

    def _setup_services(self):
        """Setup service registrations."""
        # Register singletons
        self._singletons['instagram_service'] = InstagramService()
        self._singletons['youtube_service'] = YouTubeService()
        self._singletons['file_service'] = FileService()

        self._singletons['music_search_service'] = MusicSearchService()
        self._singletons['pinterest_service'] = PinterestService()
        self._singletons['tiktok_service'] = TikTokService()
        self._singletons['likee_service'] = LikeeService()
        self._singletons['async_download_service'] = AsyncDownloadService()

    def get_instagram_service(self) -> InstagramService:
        """Get Instagram service."""
        return self._singletons['instagram_service']

    def get_youtube_service(self) -> YouTubeService:
        """Get YouTube service."""
        return self._singletons['youtube_service']

    def get_file_service(self) -> FileService:
        """Get file service."""
        return self._singletons['file_service']

    def get_music_search_service(self) -> MusicSearchService:
        """Get music search service."""
        return self._singletons['music_search_service']

    def get_telegram_service(self, bot_token: str) -> TelegramService:
        """Get Telegram service (new instance for each bot token)."""
        return TelegramService(BotToken(bot_token))

    def get_pinterest_service(self) -> PinterestService:
        """Get Pinterest service."""
        return self._singletons['pinterest_service']

    def get_tiktok_service(self) -> TikTokService:
        """Get TikTok service."""
        return self._singletons['tiktok_service']

    def get_likee_service(self) -> LikeeService:
        """Get Likee service."""
        return self._singletons['likee_service']

    def get_download_media_use_case(self, bot_token: str) -> DownloadMediaUseCase:
        """Get download media use case."""
        return DownloadMediaUseCase(
            instagram_service=self.get_instagram_service(),
            youtube_service=self.get_youtube_service(),
            pinterest_service=self.get_pinterest_service(),
            tiktok_service=self.get_tiktok_service(),
            likee_service=self.get_likee_service(),
            telegram_service=self.get_telegram_service(bot_token),
            file_service=self.get_file_service(),
            async_download_service=self.get_async_download_service(),
        )

    def get_search_music_use_case(self) -> SearchMusicUseCase:
        """Get search music use case."""
        return SearchMusicUseCase(
            music_search_service=self.get_music_search_service()
        )

    def get_download_from_shortcode_use_case(self, bot_token: str) -> DownloadFromShortcodeUseCase:
        """Get download from shortcode use case."""
        return DownloadFromShortcodeUseCase(
            music_search_service=self.get_music_search_service(),
            telegram_service=self.get_telegram_service(bot_token),
            file_service=self.get_file_service()
        )

    def get_async_download_service(self) -> AsyncDownloadService:
        """Get async download service."""
        return self._singletons['async_download_service']

    def get_error_notification_service(self, bot_token: str) -> ErrorNotificationService:
        """Get error notification service."""
        return ErrorNotificationService(bot_token)


# Global container instance
container = Container()
