"""Celery application configuration."""
import os
from celery import Celery
from src.infrastructure.config.settings import settings

# Create Celery app
celery_app = Celery(
    'instasaver',
    broker=settings.celery.broker_url,
    backend=settings.celery.result_backend,
    include=[
        'src.infrastructure.tasks.download_tasks',
    ]
)

# Celery configuration
celery_app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30 minutes
    task_soft_time_limit=25 * 60,  # 25 minutes
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
    result_expires=3600,  # 1 hour
    task_always_eager=False,  # Set to True for testing
    broker_connection_retry_on_startup=True,
    # Retry policy configuration
    task_acks_late=True,
    task_reject_on_worker_lost=True,
    task_default_retry_delay=30,
    task_default_max_retries=3,
    task_default_retry_backoff=True,
    task_default_retry_jitter=True,
    # Error handling
    task_ignore_result=False,
    task_store_errors_even_if_ignored=True,
    # Result backend settings
    result_backend_transport_options={
        'retry_policy': {
            'timeout': 5.0
        }
    }
)

# Task routing
celery_app.conf.task_routes = {
    'src.infrastructure.tasks.download_tasks.*': {'queue': 'downloads'},
    'test_task': {'queue': 'downloads'},
}

if __name__ == '__main__':
    celery_app.start()
