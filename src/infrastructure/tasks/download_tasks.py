"""Celery tasks for media downloads."""
import logging
import async<PERSON>
from typing import Dict, Any
from celery import current_task

from src.infrastructure.tasks.celery_app import celery_app

from src.application.dtos.download_dtos import DownloadMediaRequest
from src.application.dtos.search_dtos import DownloadFromShortcodeRequest as DownloadFromShortcodeRequestDto
from src.domain.value_objects.identifiers import ChatId, BotToken
from src.infrastructure.services.telegram_service import TelegramService
from src.infrastructure.services.error_notification_service import get_error_notification_service


logger = logging.getLogger(__name__)


@celery_app.task(
    bind=True, 
    name='test_task',
    max_retries=3,
    default_retry_delay=60,
    autoretry_for=(Exception,),
    retry_backoff=True,
    retry_jitter=True
)
def test_task(self) -> Dict[str, Any]:
    """
    Simple test task to debug Celery issues.
    """
    try:
        logger.info("Test task started")
        
        return {
            'success': True,
            'message': 'Test task completed successfully',
            'task_id': self.request.id
        }

    except Exception as exc:
        logger.error(f"Error in test_task: {exc}")
        raise


@celery_app.task(
    bind=True, 
    name='download_media_task',
    max_retries=5,
    default_retry_delay=3,
    autoretry_for=(Exception,),
    retry_backoff=True,
    retry_jitter=True
)
def download_media_task(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Celery task for downloading media from various platforms.

    Args:
        request_data: Dictionary containing download request parameters

    Returns:
        Dictionary with task result
    """
    try:
        logger.info(f"Download media task started with data: {request_data}")

        # Create request object
        request = DownloadMediaRequest(
            chat_id=request_data['chat_id'],
            url=request_data['url'],
            bot_token=request_data['bot_token'],
            media_type=request_data['media_type'],
            video_format=request_data.get('video_format', '720p')
        )

        # Get use case from container (dynamic import to avoid circular dependency)
        from src.infrastructure.config.container import container
        use_case = container.get_download_media_use_case(request.bot_token)

        # Execute the task and get the result
        result = asyncio.run(use_case.execute_task(request))

        return {
            'success': result.success,
            'message': result.message,
            'file_id': result.file_id,
            'task_id': self.request.id
        }


    except Exception as exc:
        logger.error(f"Error in download_media_task: {exc}")

        if self.request.retries < self.max_retries:
            retry_delay = self.default_retry_delay * (2 ** self.request.retries)
            
            logger.info(f"Retrying download_media_task in {retry_delay} seconds. "
                       f"Attempt {self.request.retries + 1}/{self.max_retries}")
            
            # Retry the task
            raise self.retry(
                exc=exc,
                countdown=retry_delay,
                max_retries=self.max_retries
            )
        else:
            logger.error(f"Max retries exceeded for download_media_task: {exc}")

            # Notify user about the failure
            asyncio.run(_notify_user_about_failure(request_data, exc))
            
            # Notify admin about the error
            asyncio.run(_notify_admin_about_failure(request_data, exc))
            
            raise


@celery_app.task(
    bind=True, 
    name='download_from_shortcode_task',
    max_retries=5,
    default_retry_delay=3,
    autoretry_for=(Exception,),
    retry_backoff=True,
    retry_jitter=True
)
def download_from_shortcode_task(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Celery task for downloading media from shortcode.
    
    Args:
        request_data: Dictionary containing shortcode request parameters
        
    Returns:
        Dictionary with task result
    """
    try:
        logger.info(f"Download from shortcode task started with data: {request_data}")

        # Create request object
        request = DownloadFromShortcodeRequestDto(
            chat_id=request_data['chat_id'],
            shortcode=request_data['shortcode'],
            bot_token=request_data['bot_token'],
            media_type=request_data['media_type']
        )

        # Get use case from container (dynamic import to avoid circular dependency)
        from src.infrastructure.config.container import container
        use_case = container.get_download_from_shortcode_use_case(request.bot_token)

        # Execute download
        result = asyncio.run(use_case.execute(request))

        return {
            'success': result.success,
            'message': result.message,
            'file_id': result.file_id,
            'task_id': self.request.id
        }

    except Exception as exc:
        logger.error(f"Error in download_from_shortcode_task: {exc}")

        # Check if we should retry
        if self.request.retries < self.max_retries:
            # Calculate retry delay with exponential backoff
            retry_delay = self.default_retry_delay * (2 ** self.request.retries)

            logger.info(f"Retrying download_from_shortcode_task in {retry_delay} seconds. "
                       f"Attempt {self.request.retries + 1}/{self.max_retries}")

            # Retry the task
            raise self.retry(
                exc=exc,
                countdown=retry_delay,
                max_retries=self.max_retries
            )
        else:
            # Max retries exceeded
            logger.error(f"Max retries exceeded for download_from_shortcode_task: {exc}")
            
            # Notify user about the failure
            asyncio.run(_notify_user_about_failure(request_data, exc))
            
            # Notify admin about the error
            asyncio.run(_notify_admin_about_failure(request_data, exc))
            
            raise


async def _notify_user_about_failure(request_data: Dict[str, Any], error: Exception) -> None:
    """
    Notify user about download failure when max retries are exceeded.
    
    Args:
        request_data: The original request data containing chat_id and bot_token
        error: The exception that caused the failure
    """
    try:
        if not request_data or 'chat_id' not in request_data or 'bot_token' not in request_data:
            logger.warning("Cannot send user notification - missing required data")
            return

        chat_id = ChatId(request_data['chat_id'])
        bot_token = BotToken(request_data['bot_token'])

        telegram_service = TelegramService(bot_token)

        user_message = "✅ Hozirda ushbu mediani yuklab bo'lmadi.\n\n" \
                      "Iltimos, keyinroq qayta urinib ko'ring yoki " \
                      "boshqa URL manzilini sinab ko'ring."

        await telegram_service.send_message(chat_id, user_message)
        logger.info(f"User notification sent to chat_id: {chat_id.value}")

    except Exception as e:
        logger.error(f"Failed to send user notification: {e}")


async def _notify_admin_about_failure(request_data: Dict[str, Any], error: Exception) -> None:
    """
    Notify admin about download failure when max retries are exceeded.
    
    Args:
        request_data: The original request data
        error: The exception that caused the failure
    """
    try:
        if not request_data or 'bot_token' not in request_data:
            logger.warning("Cannot send admin notification - missing bot_token")
            return

        bot_token = request_data['bot_token']
        error_service = get_error_notification_service(bot_token)

        error_message = f"[download_media_task] Max retries exceeded for download task\n" \
                       f"Error: {str(error)}\n" \
                       f"Request data: {request_data}\n" \
                       f"Task ID: {current_task.request.id if current_task else 'Unknown'}"

        await error_service.notify_admin_error(error_message)
        logger.info("Admin notification sent about max retries exceeded")

    except Exception as e:
        logger.error(f"Failed to send admin notification: {e}")
