"""YouTube service implementation using FastSaver API only."""
import logging
from typing import Optional
import httpx

from src.domain.interfaces.services import IYouTubeService
from src.domain.entities.download_result import DownloadResult
from src.domain.value_objects.url import Url
from src.infrastructure.config.settings import settings
from src.infrastructure.utils.text_utils import TextUtils

logger = logging.getLogger(__name__)


class YouTubeService(IYouTubeService):
    """YouTube service implementation using FastSaver API only."""

    def __init__(self):
        # Always use FastSaver API
        self.get_info_url = settings.youtube.fastsaver_get_info_url
        self.api_token = settings.youtube.fastsaver_api_token

    async def get_media_info(self, url: Url):
        """Get media information from URL."""
        # This method is required by the interface but not used for YouTube
        # YouTube downloads are handled directly
        raise NotImplementedError("Use download_video method for YouTube")

    async def download_media(self, media_item):
        """Download media content."""
        # This method is required by the interface but not used for YouTube
        # YouTube downloads are handled directly
        raise NotImplementedError("Use download_video method for YouTube")

    async def download_video(self, url: Url, bot_username: str = "instasaver_bot", video_format: Optional[str] = None) -> DownloadResult:
        """Download a YouTube video using FastSaver API."""
        return await self._download_via_fastsaver_api(url, "video", bot_username, video_format)

    async def download_audio(self, url: Url, bot_username: str = "instasaver_bot", video_format: Optional[str] = None) -> DownloadResult:
        """Download a YouTube video as audio using FastSaver API."""
        return await self._download_via_fastsaver_api(url, "audio", bot_username, video_format)

    async def _get_video_info_from_fastsaver(self, video_id: str) -> Optional[dict]:
        """Get video information from FastSaver API get-info endpoint."""
        try:
            # Construct YouTube URL for the video
            youtube_url = f"https://youtu.be/{video_id}"

            # Prepare API request
            params = {
                "url": youtube_url,
                "token": self.api_token
            }

            logger.info(f"Getting video info from FastSaver API for video_id: {video_id}")

            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(self.get_info_url, params=params)
                response.raise_for_status()

                data = response.json()

                # Check for API errors
                if data.get("error", False):
                    api_error_msg = data.get('message', 'Unknown error')
                    logger.error(f"FastSaver get-info API returned error for video_id {video_id}: {api_error_msg}")
                    return None

                logger.info(f"Successfully got video info from FastSaver API for video_id: {video_id}")
                return data

        except Exception as e:
            logger.warning(f"Failed to get video info from FastSaver API for {video_id}: {e}")

        return None

    async def _get_video_title(self, video_id: str) -> str:
        """Get video title using FastSaver API get-info endpoint."""
        try:
            video_info = await self._get_video_info_from_fastsaver(video_id)
            if video_info:
                title = video_info.get("title")
                if title:
                    cleaned_title = TextUtils.clean_text(title)
                    logger.info(f"Got title from FastSaver API: {repr(cleaned_title)}")
                    return cleaned_title

            logger.warning(f"Could not get title from FastSaver API for video_id: {video_id}")

        except Exception as e:
            logger.warning(f"Failed to get video title for {video_id}: {e}")

        # Return video ID as fallback
        return f"YouTube Video {video_id}"

    async def _get_video_duration(self, video_id: str) -> Optional[int]:
        """Get video duration using FastSaver API get-info endpoint."""
        try:
            video_info = await self._get_video_info_from_fastsaver(video_id)
            if video_info:
                duration = video_info.get("duration")
                if duration:
                    try:
                        if isinstance(duration, str):
                            time_parts = duration.split(":")
                            if len(time_parts) == 2:  # MM:SS
                                duration_seconds = int(time_parts[0]) * 60 + int(time_parts[1])
                            elif len(time_parts) == 3:  # HH:MM:SS
                                duration_seconds = int(time_parts[0]) * 3600 + int(time_parts[1]) * 60 + int(time_parts[2])
                            else:
                                duration_seconds = int(duration) if duration.isdigit() else None
                        elif isinstance(duration, (int, float)):
                            duration_seconds = int(duration)
                        else:
                            duration_seconds = None

                        if duration_seconds:
                            logger.info(f"Got duration from FastSaver API: {duration_seconds} seconds")
                            return duration_seconds
                    except (ValueError, AttributeError):
                        logger.warning(f"Could not parse duration from FastSaver API: {duration}")

            logger.warning(f"Could not get duration from FastSaver API for video_id: {video_id}")
            return None

        except Exception as e:
            logger.warning(f"Failed to get video duration for {video_id}: {e}")

        return None  # Return None if duration not found

    async def _download_format_with_retry(self, params: dict, format_name: str) -> dict:
        """Download with retry logic for a specific format."""
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.get(settings.youtube.fastsaver_download_url, params=params)
            response.raise_for_status()
            return response.json()

    async def _download_via_fastsaver_api(self, url: Url, media_type: str = "video", bot_username: str = "instasaver_bot", video_format: Optional[str] = None) -> DownloadResult:
        """Download YouTube media using FastSaver API."""
        try:
            # Extract video ID from URL
            video_id = url.extract_youtube_video_id()
            if not video_id:
                return DownloadResult(
                    success=False,
                    message="Could not extract video ID from URL"
                )

            # Determine format based on media_type
            if media_type == "audio":
                # Try different audio format options
                if video_format and video_format != "mp3":
                    logger.warning(f"Invalid format {video_format} for audio download, trying mp3")
                format_param = "mp3"
            else:
                # Use provided format or default from config
                format_param = video_format or settings.youtube.default_video_format

                # Validate that mp3 is not being used for video
                if format_param == "mp3":
                    logger.warning("mp3 format cannot be used for video download, using default video format")
                    format_param = settings.youtube.default_video_format

                # Validate format is supported
                if format_param not in settings.youtube.available_formats:
                    logger.warning(f"Unsupported video format: {format_param}, using default: {settings.youtube.default_video_format}")
                    format_param = settings.youtube.default_video_format

            # Prepare API request
            params = {
                "video_id": video_id,
                "format": format_param,
                "bot_username": bot_username,
                "token": settings.youtube.fastsaver_api_token
            }

            logger.info(f"Downloading via FastSaver API: {video_id}, format: {format_param}")

            # Try the requested format first, then fallback formats
            formats_to_try = [format_param]
            if media_type == "audio":
                # For audio, try mp3 first, then mp4 as fallback
                if format_param != "mp4":
                    formats_to_try.append("mp4")
                # Also try 720p as another fallback for audio
                if format_param != "720p":
                    formats_to_try.append("720p")
            elif media_type == "video":
                # For video, try requested format, then 720p as fallback
                if format_param != "720p":
                    formats_to_try.append("720p")

            last_error = None
            for attempt_format in formats_to_try:
                try:
                    params["format"] = attempt_format
                    logger.info(f"Trying format: {attempt_format} with params: {params}")

                    # Use retry logic for each format attempt
                    data = await self._download_format_with_retry(params, attempt_format)

                    # Check for API errors
                    if data.get("error", False):
                        error_msg = data.get('message', 'Unknown error')
                        logger.warning(f"FastSaver API error for format {attempt_format}: {error_msg}")
                        last_error = error_msg
                        continue  # Try next format

                    # Success - break out of loop
                    break

                except Exception as e:
                    logger.warning(f"Error for format {attempt_format}: {e}")
                    last_error = str(e)
                    continue  # Try next format
            else:
                # All formats failed
                logger.error(f"All formats failed for video {video_id}. Last error: {last_error}")
                return DownloadResult(
                    success=False,
                    message=f"Video format not available for download. Tried formats: {formats_to_try}. Last error: {last_error}"
                )

            # Extract file_id from response
            file_id = data.get("file_id")
            if not file_id:
                logger.error(f"No file_id returned from FastSaver API for video {video_id}")
                return DownloadResult(
                    success=False,
                    message="Download service did not return valid file"
                )

            # Validate that the returned media type matches what we requested
            returned_media_type = data.get("media_type")
            if returned_media_type and returned_media_type != media_type:
                logger.warning(f"FastSaver API returned {returned_media_type} but we requested {media_type}")
                # This is just a warning, we'll still proceed with the file_id

            # Get video title for caption
            title = data.get("title")
            logger.info(f"Raw title from FastSaver API: {repr(title)}")

            if title:
                # Clean up title using TextUtils
                title = TextUtils.clean_text(title)
                logger.info(f"Cleaned title: {repr(title)}")

            if not title:
                logger.info("No title from API, trying to get from YouTube page")
                title = await self._get_video_title(video_id)
                if title:
                    title = TextUtils.clean_text(title)
                logger.info(f"Title from YouTube page: {repr(title)}")

            # Get duration from API response
            duration = data.get("duration")
            if duration and isinstance(duration, str):
                # Convert duration string to seconds if needed
                try:
                    # Handle formats like "3:45" or "1:23:45"
                    time_parts = duration.split(":")
                    if len(time_parts) == 2:  # MM:SS
                        duration_seconds = int(time_parts[0]) * 60 + int(time_parts[1])
                    elif len(time_parts) == 3:  # HH:MM:SS
                        duration_seconds = int(time_parts[0]) * 3600 + int(time_parts[1]) * 60 + int(time_parts[2])
                    else:
                        duration_seconds = int(duration) if duration.isdigit() else None
                except (ValueError, AttributeError):
                    duration_seconds = None
            elif duration and isinstance(duration, (int, float)):
                duration_seconds = int(duration)
            else:
                duration_seconds = None

            # If no duration from API, try to get from YouTube page
            if duration_seconds is None:
                logger.info("No duration from API, trying to get from YouTube page")
                duration_seconds = await self._get_video_duration(video_id)
                logger.info(f"Duration from YouTube page: {duration_seconds} seconds")

            logger.info(f"FastSaver download successful: file_id={file_id}, format: {params['format']}, returned_media_type: {returned_media_type}, title: {title}, duration: {duration_seconds}")

            return DownloadResult(
                success=True,
                message=f"YouTube {media_type} downloaded successfully via FastSaver API (format: {params['format']})",
                file_path=None,  # No local file path since we use file_id
                telegram_file_id=file_id,
                title=title,  # Return title for caption
                duration=duration_seconds  # Return duration in seconds
            )

        except httpx.HTTPError as e:
            logger.error(f"HTTP error downloading via FastSaver API: {e}")
            return DownloadResult(
                success=False,
                message=f"Network error: {str(e)}",
                title=None
            )
        except Exception as e:
            logger.error(f"Error downloading via FastSaver API: {e}")
            return DownloadResult(
                success=False,
                message=f"Error: {str(e)}",
                title=None
            )
