"""Facebook service implementation."""
import logging

from urllib.parse import quote
import httpx

from src.domain.interfaces.services import IFacebookService
from src.domain.value_objects.url import Url
from src.domain.entities.media_item import MediaItem, MediaType
from src.infrastructure.config.settings import settings

logger = logging.getLogger(__name__)


class FacebookService(IFacebookService):
    """Facebook service implementation using FastSaver API."""

    def __init__(self):
        self.api_base_url = settings.facebook.api_base_url
        self.api_token = settings.facebook.api_token

        if not self.api_token:
            logger.warning("Facebook API token not configured. Set FACEBOOK_API_TOKEN environment variable.")

    async def get_media_info(self, url: Url) -> MediaItem:
        """Get media information from Facebook URL."""
        if not self.api_token:
            raise Exception("Facebook API token not configured. Please set FACEBOOK_API_TOKEN environment variable.")

        try:
            encoded_url = quote(url.value, safe='')
            api_url = f"{self.api_base_url}?url={encoded_url}&token={self.api_token}"

            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(api_url)
                response.raise_for_status()

                data = response.json()
                logger.info(f"Facebook API response: {data}")

                if data.get("error", True):
                    error_msg = data.get("message", "Unknown error from Facebook API")
                    logger.error(f"Facebook API error: {error_msg}")
                    raise Exception(f"Facebook API error: {error_msg}")

                # Extract media information from API response
                download_url = data.get("download_url")
                if not download_url:
                    raise Exception("No download URL found in Facebook API response")

                caption = data.get("caption") or ""
                thumb_url = data.get("thumb")
                media_type_str = data.get("type", "video")
                duration = data.get("duration")

                # Determine media type
                if media_type_str == "video":
                    media_type = MediaType.VIDEO
                elif media_type_str == "photo":
                    media_type = MediaType.PHOTO
                else:
                    media_type = MediaType.VIDEO  # Default to video

                return MediaItem(
                    url=url.value,
                    media_type=media_type,
                    download_url=download_url,
                    thumbnail_url=thumb_url,
                    caption=caption,
                    duration=duration
                )

        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error getting Facebook media info: {e}")
            raise Exception(f"Failed to get Facebook media info: HTTP {e.response.status_code}")
        except Exception as e:
            logger.error(f"Error getting Facebook media info: {e}")
            raise Exception(f"Failed to get Facebook media info: {str(e)}")

    async def download_media(self, media_item) -> bytes:
        """Download media content."""
        try:
            download_url = None

            if hasattr(media_item, 'download_url'):
                download_url = media_item.download_url
            elif isinstance(media_item, dict):
                download_url = media_item.get('download_url')
            else:
                raise ValueError("Invalid media item format")

            if not download_url:
                raise Exception("No download URL available")

            async with httpx.AsyncClient(timeout=120.0, follow_redirects=True) as client:
                response = await client.get(download_url)
                response.raise_for_status()

                logger.info(f"Downloaded {len(response.content)} bytes from Facebook")
                return response.content

        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error downloading Facebook media: {e}")
            raise Exception(f"Failed to download Facebook media: HTTP {e.response.status_code}")
        except Exception as e:
            logger.error(f"Error downloading Facebook media: {e}")
            raise Exception(f"Failed to download Facebook media: {str(e)}")

    async def download_thumbnail(self, thumbnail_url: str) -> bytes:
        """Download thumbnail from Facebook."""
        try:
            if not thumbnail_url:
                raise Exception("No thumbnail URL available")

            async with httpx.AsyncClient(timeout=60.0, follow_redirects=True) as client:
                response = await client.get(thumbnail_url)
                response.raise_for_status()

                logger.info(f"Downloaded {len(response.content)} bytes of Facebook thumbnail")
                return response.content

        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error downloading Facebook thumbnail: {e}")
            raise Exception(f"Failed to download Facebook thumbnail: HTTP {e.response.status_code}")
        except Exception as e:
            logger.error(f"Error downloading Facebook thumbnail: {e}")
            raise Exception(f"Failed to download Facebook thumbnail: {str(e)}")
