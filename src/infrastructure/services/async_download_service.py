"""Async download service that uses Celery for background processing."""
import logging
from typing import Dict, Any
from src.infrastructure.tasks.celery_app import celery_app
from src.application.dtos.download_dtos import DownloadMediaRequest
from src.application.dtos.search_dtos import DownloadFromShortcodeRequest as DownloadFromShortcodeRequestDto
from dataclasses import asdict

logger = logging.getLogger(__name__)


class AsyncDownloadService:
    """Service for queuing download tasks with Celery."""
    
    def __init__(self):
        """Initialize the async download service."""
        pass
    
    def queue_download_task(self, request: DownloadMediaRequest) -> Dict[str, Any]:
        """
        Queue a download task for background processing.
        
        Args:
            request: Download media request
            
        Returns:
            Dictionary with task information
        """
        try:
            # Prepare request data for Celery (dict)
            request_data = asdict(request)

            # Queue the task using dynamic import to avoid circular dependency
            from src.infrastructure.tasks.download_tasks import download_media_task
            task = download_media_task.apply_async(args=[request_data], queue='downloads')
 
            return {
                'success': True,
                'task_id': task.id,
                'status': 'queued',
                'message': 'Download task queued successfully'
            }
            
        except Exception as exc:
            logger.error(f"Error queuing download task: {exc}")
            return {
                'success': False,
                'error': str(exc),
                'message': 'Failed to queue download task'
            }
    
    def queue_shortcode_download_task(self, request: DownloadFromShortcodeRequestDto) -> Dict[str, Any]:
        """
        Queue a shortcode download task for background processing.
        
        Args:
            request: Download from shortcode request
            
        Returns:
            Dictionary with task information
        """
        try:
            # Prepare request data for Celery (dict)
            request_data = asdict(request)
            
            # Queue the task using dynamic import to avoid circular dependency
            from src.infrastructure.tasks.download_tasks import download_from_shortcode_task
            task = download_from_shortcode_task.apply_async(args=[request_data], queue='downloads')
            
            return {
                'success': True,
                'task_id': task.id,
                'status': 'queued',
                'message': 'Shortcode download task queued successfully'
            }
            
        except Exception as exc:
            logger.error(f"Error queuing shortcode download task: {exc}")
            return {
                'success': False,
                'error': str(exc),
                'message': 'Failed to queue shortcode download task'
            }
    
    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """
        Get the status of a Celery task.
        
        Args:
            task_id: Celery task ID
            
        Returns:
            Dictionary with task status
        """
        try:
            task = celery_app.AsyncResult(task_id)
            
            if task.state == 'PENDING':
                response = {
                    'state': task.state,
                    'status': 'Task is pending...'
                }
            elif task.state == 'PROGRESS':
                response = {
                    'state': task.state,
                    'status': task.info.get('status', ''),
                    'current': task.info.get('current', 0),
                    'total': task.info.get('total', 100)
                }
            elif task.state == 'SUCCESS':
                response = {
                    'state': task.state,
                    'result': task.result
                }
            else:
                response = {
                    'state': task.state,
                    'error': str(task.info)
                }
            
            return response
            
        except Exception as exc:
            logger.error(f"Error getting task status: {exc}")
            return {
                'state': 'ERROR',
                'error': str(exc)
            }
    
    def cancel_task(self, task_id: str) -> Dict[str, Any]:
        """
        Cancel a Celery task.
        
        Args:
            task_id: Celery task ID
            
        Returns:
            Dictionary with cancellation result
        """
        try:
            celery_app.control.revoke(task_id, terminate=True)
            
            return {
                'success': True,
                'message': f'Task {task_id} cancelled successfully'
            }
            
        except Exception as exc:
            logger.error(f"Error cancelling task: {exc}")
            return {
                'success': False,
                'error': str(exc),
                'message': 'Failed to cancel task'
            } 